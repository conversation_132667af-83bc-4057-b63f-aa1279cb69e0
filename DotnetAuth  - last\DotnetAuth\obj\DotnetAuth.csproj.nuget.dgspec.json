{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\FINAL PR WITH REACT\\Backend-Project-Last\\DotnetAuth  - last\\DotnetAuth\\DotnetAuth.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\FINAL PR WITH REACT\\Backend-Project-Last\\DotnetAuth  - last\\DotnetAuth\\DotnetAuth.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\FINAL PR WITH REACT\\Backend-Project-Last\\DotnetAuth  - last\\DotnetAuth\\DotnetAuth.csproj", "projectName": "DotnetAuth", "projectPath": "C:\\Users\\<USER>\\Desktop\\FINAL PR WITH REACT\\Backend-Project-Last\\DotnetAuth  - last\\DotnetAuth\\DotnetAuth.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\FINAL PR WITH REACT\\Backend-Project-Last\\DotnetAuth  - last\\DotnetAuth\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "Google.Apis.Auth": {"target": "Package", "version": "[1.69.0, )"}, "Microsoft.AspNetCore.Authentication.Google": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}, "Scalar.AspNetCore": {"target": "Package", "version": "[2.2.1, )"}, "SendGrid": {"target": "Package", "version": "[9.29.3, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}