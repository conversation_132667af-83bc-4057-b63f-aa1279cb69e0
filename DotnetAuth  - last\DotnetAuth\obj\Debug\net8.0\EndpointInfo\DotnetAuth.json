{"openapi": "3.0.1", "info": {"title": "User <PERSON>", "description": "Services to Authenticate user", "version": "v1"}, "paths": {"/api/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/verify-otp": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/forgot-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/user/{id}": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/revoke-refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/current-user": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"ForgotPasswordRequest": {"required": ["confirmPassword", "email", "newPassword", "oldPassword"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "oldPassword": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "Gender": {"enum": [0, 1], "type": "integer", "format": "int32"}, "RefreshTokenRequest": {"type": "object", "properties": {"refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResetPasswordRequest": {"required": ["confirmPassword", "newPassword"], "type": "object", "properties": {"newPassword": {"minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "UserLoginRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "rememberMe": {"type": "boolean"}}, "additionalProperties": false}, "UserRegisterRequest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "gender": {"$ref": "#/components/schemas/Gender"}, "role": {"$ref": "#/components/schemas/UserRole"}}, "additionalProperties": false}, "UserRole": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "VerifyOtpRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "otp": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "Please enter a valid token in the following format: {your token here} do not add the word 'Bearer' before it.", "scheme": "Bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}