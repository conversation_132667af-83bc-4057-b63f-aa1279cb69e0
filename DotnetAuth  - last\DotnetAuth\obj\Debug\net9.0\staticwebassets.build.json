{"Version": 1, "Hash": "xYHH/+T6QDmJNl1mwAYG/vJoIWgQ1QbOspC1DRGqC3Y=", "Source": "DotnetAuth", "BasePath": "_content/DotnetAuth", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "DotnetAuth\\wwwroot", "Source": "DotnetAuth", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "Pattern": "**"}], "Assets": [{"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\30cda491-3fc4-4ebf-b0af-fbc6ef5d51c3_638819029132474981_oQ_kaNdo_400x400.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/30cda491-3fc4-4ebf-b0af-fbc6ef5d51c3_638819029132474981_oQ_kaNdo_400x400#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k1xddxar21", "Integrity": "iJkTxlUWfNQ0ZEOX/Hh17FowZZwrs/+MwWkYxEG3fek=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\30cda491-3fc4-4ebf-b0af-fbc6ef5d51c3_638819029132474981_oQ_kaNdo_400x400.png", "FileLength": 30635, "LastWriteTime": "2025-05-03T18:01:53+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\32f09b3d-665f-4bdd-b633-09013cfeb90f_638815635307753791_ChatGPT Image Apr 2, 2025, 11_09_20 PM.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/32f09b3d-665f-4bdd-b633-09013cfeb90f_638815635307753791_ChatGPT Image Apr 2, 2025, 11_09_20 PM#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "at3n64ki55", "Integrity": "Dol7R/HYtKgVZVRWEHwmgZaPcGjWFKtJ5B+/2ErsXkA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\32f09b3d-665f-4bdd-b633-09013cfeb90f_638815635307753791_ChatGPT Image Apr 2, 2025, 11_09_20 PM.png", "FileLength": 2551722, "LastWriteTime": "2025-04-29T19:45:30+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\32f09b3d-665f-4bdd-b633-09013cfeb90f_638816662622472539_oQ_kaNdo_400x400.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/32f09b3d-665f-4bdd-b633-09013cfeb90f_638816662622472539_oQ_kaNdo_400x400#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k1xddxar21", "Integrity": "iJkTxlUWfNQ0ZEOX/Hh17FowZZwrs/+MwWkYxEG3fek=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\32f09b3d-665f-4bdd-b633-09013cfeb90f_638816662622472539_oQ_kaNdo_400x400.png", "FileLength": 30635, "LastWriteTime": "2025-05-01T00:17:42+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\4ce87248-bcca-4540-93e0-f2c2c5384159_638817178513916354_GiKAclQWEAAGISX.jpeg", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/4ce87248-bcca-4540-93e0-f2c2c5384159_638817178513916354_GiKAclQWEAAGISX#[.{fingerprint}]?.jpeg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9ujzhzvm66", "Integrity": "zyPEz9OdQSqKRDyOIVy14FW8iSCUdUNDjCp8eTWstpo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\4ce87248-bcca-4540-93e0-f2c2c5384159_638817178513916354_GiKAclQWEAAGISX.jpeg", "FileLength": 93061, "LastWriteTime": "2025-05-01T14:37:31+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\4ce87248-bcca-4540-93e0-f2c2c5384159_638817178782470257_111522964.jpg", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/4ce87248-bcca-4540-93e0-f2c2c5384159_638817178782470257_111522964#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zb6tj3uwwx", "Integrity": "sXuURPDxu0SLzwcFaKCsEu0c3NV9AiHxQIA03qbO3E0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\4ce87248-bcca-4540-93e0-f2c2c5384159_638817178782470257_111522964.jpg", "FileLength": 22800, "LastWriteTime": "2025-05-01T14:37:58+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\4ce87248-bcca-4540-93e0-f2c2c5384159_638817181318220582_w8zamoi77b631.jpg", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/4ce87248-bcca-4540-93e0-f2c2c5384159_638817181318220582_w8zamoi77b631#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8k2jy9suxp", "Integrity": "uPm6i1dtCnxIJ1cFvd2PcjLlMVoPNNugi+wXHPmjReg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\4ce87248-bcca-4540-93e0-f2c2c5384159_638817181318220582_w8zamoi77b631.jpg", "FileLength": 441947, "LastWriteTime": "2025-05-01T14:42:11+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\594e422a-3281-4e9e-8746-44f9c139f419_638816664973393013_AhmedV19.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/594e422a-3281-4e9e-8746-44f9c139f419_638816664973393013_AhmedV19#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rwx8dhcqng", "Integrity": "VVG5E4BISq7gXHuqXymvExKENn1wvFdUq4nkMR28WeY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\594e422a-3281-4e9e-8746-44f9c139f419_638816664973393013_AhmedV19.png", "FileLength": 1660581, "LastWriteTime": "2025-05-01T00:21:37+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\594e422a-3281-4e9e-8746-44f9c139f419_638816670178340266_avezvlr_2025-01-05_19-38-29.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/594e422a-3281-4e9e-8746-44f9c139f419_638816670178340266_avezvlr_2025-01-05_19-38-29#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1088ca372s", "Integrity": "f0FZLooCQbCBQpTHXszZrb3oaoX3LJ/3Rby9PNCcYDA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\594e422a-3281-4e9e-8746-44f9c139f419_638816670178340266_avezvlr_2025-01-05_19-38-29.png", "FileLength": 218241, "LastWriteTime": "2025-05-01T00:30:17+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\594e422a-3281-4e9e-8746-44f9c139f419_638816673384174248_Screenshot 2025-02-23 202058.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/594e422a-3281-4e9e-8746-44f9c139f419_638816673384174248_Screenshot 2025-02-23 202058#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5uzg5oqdvr", "Integrity": "k4EofsOL7xeqGRE+WqZzBBXQE4sL5H07CT5qBKXQkvw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\594e422a-3281-4e9e-8746-44f9c139f419_638816673384174248_Screenshot 2025-02-23 202058.png", "FileLength": 514465, "LastWriteTime": "2025-05-01T00:35:38+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\8f133d3f-0107-4cfe-9181-2ad575f2f1b6_638816689686001066_c4375c65eb82f284d24f53d6be49e036.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/8f133d3f-0107-4cfe-9181-2ad575f2f1b6_638816689686001066_c4375c65eb82f284d24f53d6be49e036#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dinrx7zu6e", "Integrity": "xZzkdTEb+5YCf3TEqLIzSBW/SLXHBnN0fHcepYlH1fE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\8f133d3f-0107-4cfe-9181-2ad575f2f1b6_638816689686001066_c4375c65eb82f284d24f53d6be49e036.png", "FileLength": 969969, "LastWriteTime": "2025-05-01T01:02:48+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\b624475d-6b32-4320-85f6-5dea263b44bb_638817196113290573_db1e6f380aca333fca9f6a7e155a0fb3.gif", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/b624475d-6b32-4320-85f6-5dea263b44bb_638817196113290573_db1e6f380aca333fca9f6a7e155a0fb3#[.{fingerprint}]?.gif", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gq6w51lmwl", "Integrity": "6Hy0H9uWqw+3Vagzl7GTEQafajy9ryCdPhOzPvT5/A0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\b624475d-6b32-4320-85f6-5dea263b44bb_638817196113290573_db1e6f380aca333fca9f6a7e155a0fb3.gif", "FileLength": 4024248, "LastWriteTime": "2025-05-01T15:06:51+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817189571640603__Miq1UZK_400x400.jpg", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817189571640603__Miq1UZK_400x400#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dkfjba56rm", "Integrity": "cwcK2BNY/+Pm9oXMWlb85o76F8CHBZkapTOFGTj5PqI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817189571640603__Miq1UZK_400x400.jpg", "FileLength": 50483, "LastWriteTime": "2025-05-01T14:55:57+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192734685591_c4375c65eb82f284d24f53d6be49e036.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192734685591_c4375c65eb82f284d24f53d6be49e036#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dinrx7zu6e", "Integrity": "xZzkdTEb+5YCf3TEqLIzSBW/SLXHBnN0fHcepYlH1fE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192734685591_c4375c65eb82f284d24f53d6be49e036.png", "FileLength": 969969, "LastWriteTime": "2025-05-01T15:01:13+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192931813656_ef6c9397f7c66d188591eb75d46c457f.jpg", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192931813656_ef6c9397f7c66d188591eb75d46c457f#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6sfcr98ok6", "Integrity": "5hnnkHtczIU5NKskw8CE934JMUnsLOTdurJ0NrknKiU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192931813656_ef6c9397f7c66d188591eb75d46c457f.jpg", "FileLength": 111603, "LastWriteTime": "2025-05-01T15:01:33+00:00"}, {"Identity": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "SourceId": "DotnetAuth", "SourceType": "Discovered", "ContentRoot": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\", "BasePath": "_content/DotnetAuth", "RelativePath": "profile-pictures/defaults/default-picture-profile#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d90hbt9f7m", "Integrity": "keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "FileLength": 5153, "LastWriteTime": "2025-04-25T19:17:03+00:00"}], "Endpoints": [{"Route": "profile-pictures/30cda491-3fc4-4ebf-b0af-fbc6ef5d51c3_638819029132474981_oQ_kaNdo_400x400.k1xddxar21.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\30cda491-3fc4-4ebf-b0af-fbc6ef5d51c3_638819029132474981_oQ_kaNdo_400x400.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30635"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"iJkTxlUWfNQ0ZEOX/Hh17FowZZwrs/+MwWkYxEG3fek=\""}, {"Name": "Last-Modified", "Value": "Sat, 03 May 2025 18:01:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k1xddxar21"}, {"Name": "label", "Value": "profile-pictures/30cda491-3fc4-4ebf-b0af-fbc6ef5d51c3_638819029132474981_oQ_kaNdo_400x400.png"}, {"Name": "integrity", "Value": "sha256-iJkTxlUWfNQ0ZEOX/Hh17FowZZwrs/+MwWkYxEG3fek="}]}, {"Route": "profile-pictures/30cda491-3fc4-4ebf-b0af-fbc6ef5d51c3_638819029132474981_oQ_kaNdo_400x400.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\30cda491-3fc4-4ebf-b0af-fbc6ef5d51c3_638819029132474981_oQ_kaNdo_400x400.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30635"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"iJkTxlUWfNQ0ZEOX/Hh17FowZZwrs/+MwWkYxEG3fek=\""}, {"Name": "Last-Modified", "Value": "Sat, 03 May 2025 18:01:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iJkTxlUWfNQ0ZEOX/Hh17FowZZwrs/+MwWkYxEG3fek="}]}, {"Route": "profile-pictures/32f09b3d-665f-4bdd-b633-09013cfeb90f_638815635307753791_ChatGPT Image Apr 2, 2025, 11_09_20 PM.at3n64ki55.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\32f09b3d-665f-4bdd-b633-09013cfeb90f_638815635307753791_ChatGPT Image Apr 2, 2025, 11_09_20 PM.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2551722"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Dol7R/HYtKgVZVRWEHwmgZaPcGjWFKtJ5B+/2ErsXkA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 29 Apr 2025 19:45:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "at3n64ki55"}, {"Name": "label", "Value": "profile-pictures/32f09b3d-665f-4bdd-b633-09013cfeb90f_638815635307753791_ChatGPT Image Apr 2, 2025, 11_09_20 PM.png"}, {"Name": "integrity", "Value": "sha256-Dol7R/HYtKgVZVRWEHwmgZaPcGjWFKtJ5B+/2ErsXkA="}]}, {"Route": "profile-pictures/32f09b3d-665f-4bdd-b633-09013cfeb90f_638815635307753791_ChatGPT Image Apr 2, 2025, 11_09_20 PM.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\32f09b3d-665f-4bdd-b633-09013cfeb90f_638815635307753791_ChatGPT Image Apr 2, 2025, 11_09_20 PM.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2551722"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Dol7R/HYtKgVZVRWEHwmgZaPcGjWFKtJ5B+/2ErsXkA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 29 Apr 2025 19:45:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Dol7R/HYtKgVZVRWEHwmgZaPcGjWFKtJ5B+/2ErsXkA="}]}, {"Route": "profile-pictures/32f09b3d-665f-4bdd-b633-09013cfeb90f_638816662622472539_oQ_kaNdo_400x400.k1xddxar21.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\32f09b3d-665f-4bdd-b633-09013cfeb90f_638816662622472539_oQ_kaNdo_400x400.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30635"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"iJkTxlUWfNQ0ZEOX/Hh17FowZZwrs/+MwWkYxEG3fek=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 00:17:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k1xddxar21"}, {"Name": "label", "Value": "profile-pictures/32f09b3d-665f-4bdd-b633-09013cfeb90f_638816662622472539_oQ_kaNdo_400x400.png"}, {"Name": "integrity", "Value": "sha256-iJkTxlUWfNQ0ZEOX/Hh17FowZZwrs/+MwWkYxEG3fek="}]}, {"Route": "profile-pictures/32f09b3d-665f-4bdd-b633-09013cfeb90f_638816662622472539_oQ_kaNdo_400x400.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\32f09b3d-665f-4bdd-b633-09013cfeb90f_638816662622472539_oQ_kaNdo_400x400.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30635"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"iJkTxlUWfNQ0ZEOX/Hh17FowZZwrs/+MwWkYxEG3fek=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 00:17:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iJkTxlUWfNQ0ZEOX/Hh17FowZZwrs/+MwWkYxEG3fek="}]}, {"Route": "profile-pictures/4ce87248-bcca-4540-93e0-f2c2c5384159_638817178513916354_GiKAclQWEAAGISX.9ujzhzvm66.jpeg", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\4ce87248-bcca-4540-93e0-f2c2c5384159_638817178513916354_GiKAclQWEAAGISX.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "93061"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"zyPEz9OdQSqKRDyOIVy14FW8iSCUdUNDjCp8eTWstpo=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ujzhzvm66"}, {"Name": "label", "Value": "profile-pictures/4ce87248-bcca-4540-93e0-f2c2c5384159_638817178513916354_GiKAclQWEAAGISX.jpeg"}, {"Name": "integrity", "Value": "sha256-zyPEz9OdQSqKRDyOIVy14FW8iSCUdUNDjCp8eTWstpo="}]}, {"Route": "profile-pictures/4ce87248-bcca-4540-93e0-f2c2c5384159_638817178513916354_GiKAclQWEAAGISX.jpeg", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\4ce87248-bcca-4540-93e0-f2c2c5384159_638817178513916354_GiKAclQWEAAGISX.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "93061"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"zyPEz9OdQSqKRDyOIVy14FW8iSCUdUNDjCp8eTWstpo=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 14:37:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zyPEz9OdQSqKRDyOIVy14FW8iSCUdUNDjCp8eTWstpo="}]}, {"Route": "profile-pictures/4ce87248-bcca-4540-93e0-f2c2c5384159_638817178782470257_111522964.jpg", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\4ce87248-bcca-4540-93e0-f2c2c5384159_638817178782470257_111522964.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22800"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"sXuURPDxu0SLzwcFaKCsEu0c3NV9AiHxQIA03qbO3E0=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 14:37:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sXuURPDxu0SLzwcFaKCsEu0c3NV9AiHxQIA03qbO3E0="}]}, {"Route": "profile-pictures/4ce87248-bcca-4540-93e0-f2c2c5384159_638817178782470257_111522964.zb6tj3uwwx.jpg", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\4ce87248-bcca-4540-93e0-f2c2c5384159_638817178782470257_111522964.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22800"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"sXuURPDxu0SLzwcFaKCsEu0c3NV9AiHxQIA03qbO3E0=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 14:37:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zb6tj3uwwx"}, {"Name": "label", "Value": "profile-pictures/4ce87248-bcca-4540-93e0-f2c2c5384159_638817178782470257_111522964.jpg"}, {"Name": "integrity", "Value": "sha256-sXuURPDxu0SLzwcFaKCsEu0c3NV9AiHxQIA03qbO3E0="}]}, {"Route": "profile-pictures/4ce87248-bcca-4540-93e0-f2c2c5384159_638817181318220582_w8zamoi77b631.8k2jy9suxp.jpg", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\4ce87248-bcca-4540-93e0-f2c2c5384159_638817181318220582_w8zamoi77b631.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "441947"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"uPm6i1dtCnxIJ1cFvd2PcjLlMVoPNNugi+wXHPmjReg=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 14:42:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8k2jy9suxp"}, {"Name": "label", "Value": "profile-pictures/4ce87248-bcca-4540-93e0-f2c2c5384159_638817181318220582_w8zamoi77b631.jpg"}, {"Name": "integrity", "Value": "sha256-uPm6i1dtCnxIJ1cFvd2PcjLlMVoPNNugi+wXHPmjReg="}]}, {"Route": "profile-pictures/4ce87248-bcca-4540-93e0-f2c2c5384159_638817181318220582_w8zamoi77b631.jpg", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\4ce87248-bcca-4540-93e0-f2c2c5384159_638817181318220582_w8zamoi77b631.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "441947"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"uPm6i1dtCnxIJ1cFvd2PcjLlMVoPNNugi+wXHPmjReg=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 14:42:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uPm6i1dtCnxIJ1cFvd2PcjLlMVoPNNugi+wXHPmjReg="}]}, {"Route": "profile-pictures/594e422a-3281-4e9e-8746-44f9c139f419_638816664973393013_AhmedV19.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\594e422a-3281-4e9e-8746-44f9c139f419_638816664973393013_AhmedV19.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1660581"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VVG5E4BISq7gXHuqXymvExKENn1wvFdUq4nkMR28WeY=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 00:21:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VVG5E4BISq7gXHuqXymvExKENn1wvFdUq4nkMR28WeY="}]}, {"Route": "profile-pictures/594e422a-3281-4e9e-8746-44f9c139f419_638816664973393013_AhmedV19.rwx8dhcqng.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\594e422a-3281-4e9e-8746-44f9c139f419_638816664973393013_AhmedV19.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1660581"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VVG5E4BISq7gXHuqXymvExKENn1wvFdUq4nkMR28WeY=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 00:21:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rwx8dhcqng"}, {"Name": "label", "Value": "profile-pictures/594e422a-3281-4e9e-8746-44f9c139f419_638816664973393013_AhmedV19.png"}, {"Name": "integrity", "Value": "sha256-VVG5E4BISq7gXHuqXymvExKENn1wvFdUq4nkMR28WeY="}]}, {"Route": "profile-pictures/594e422a-3281-4e9e-8746-44f9c139f419_638816670178340266_avezvlr_2025-01-05_19-38-29.1088ca372s.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\594e422a-3281-4e9e-8746-44f9c139f419_638816670178340266_avezvlr_2025-01-05_19-38-29.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "218241"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"f0FZLooCQbCBQpTHXszZrb3oaoX3LJ/3Rby9PNCcYDA=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 00:30:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1088ca372s"}, {"Name": "label", "Value": "profile-pictures/594e422a-3281-4e9e-8746-44f9c139f419_638816670178340266_avezvlr_2025-01-05_19-38-29.png"}, {"Name": "integrity", "Value": "sha256-f0FZLooCQbCBQpTHXszZrb3oaoX3LJ/3Rby9PNCcYDA="}]}, {"Route": "profile-pictures/594e422a-3281-4e9e-8746-44f9c139f419_638816670178340266_avezvlr_2025-01-05_19-38-29.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\594e422a-3281-4e9e-8746-44f9c139f419_638816670178340266_avezvlr_2025-01-05_19-38-29.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "218241"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"f0FZLooCQbCBQpTHXszZrb3oaoX3LJ/3Rby9PNCcYDA=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 00:30:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f0FZLooCQbCBQpTHXszZrb3oaoX3LJ/3Rby9PNCcYDA="}]}, {"Route": "profile-pictures/594e422a-3281-4e9e-8746-44f9c139f419_638816673384174248_Screenshot 2025-02-23 202058.5uzg5oqdvr.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\594e422a-3281-4e9e-8746-44f9c139f419_638816673384174248_Screenshot 2025-02-23 202058.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "514465"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"k4EofsOL7xeqGRE+WqZzBBXQE4sL5H07CT5qBKXQkvw=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 00:35:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5uzg5oqdvr"}, {"Name": "label", "Value": "profile-pictures/594e422a-3281-4e9e-8746-44f9c139f419_638816673384174248_Screenshot 2025-02-23 202058.png"}, {"Name": "integrity", "Value": "sha256-k4EofsOL7xeqGRE+WqZzBBXQE4sL5H07CT5qBKXQkvw="}]}, {"Route": "profile-pictures/594e422a-3281-4e9e-8746-44f9c139f419_638816673384174248_Screenshot 2025-02-23 202058.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\594e422a-3281-4e9e-8746-44f9c139f419_638816673384174248_Screenshot 2025-02-23 202058.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "514465"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"k4EofsOL7xeqGRE+WqZzBBXQE4sL5H07CT5qBKXQkvw=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 00:35:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-k4EofsOL7xeqGRE+WqZzBBXQE4sL5H07CT5qBKXQkvw="}]}, {"Route": "profile-pictures/8f133d3f-0107-4cfe-9181-2ad575f2f1b6_638816689686001066_c4375c65eb82f284d24f53d6be49e036.dinrx7zu6e.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\8f133d3f-0107-4cfe-9181-2ad575f2f1b6_638816689686001066_c4375c65eb82f284d24f53d6be49e036.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "969969"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xZzkdTEb+5YCf3TEqLIzSBW/SLXHBnN0fHcepYlH1fE=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 01:02:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dinrx7zu6e"}, {"Name": "label", "Value": "profile-pictures/8f133d3f-0107-4cfe-9181-2ad575f2f1b6_638816689686001066_c4375c65eb82f284d24f53d6be49e036.png"}, {"Name": "integrity", "Value": "sha256-xZzkdTEb+5YCf3TEqLIzSBW/SLXHBnN0fHcepYlH1fE="}]}, {"Route": "profile-pictures/8f133d3f-0107-4cfe-9181-2ad575f2f1b6_638816689686001066_c4375c65eb82f284d24f53d6be49e036.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\8f133d3f-0107-4cfe-9181-2ad575f2f1b6_638816689686001066_c4375c65eb82f284d24f53d6be49e036.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "969969"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xZzkdTEb+5YCf3TEqLIzSBW/SLXHBnN0fHcepYlH1fE=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 01:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xZzkdTEb+5YCf3TEqLIzSBW/SLXHBnN0fHcepYlH1fE="}]}, {"Route": "profile-pictures/b624475d-6b32-4320-85f6-5dea263b44bb_638817196113290573_db1e6f380aca333fca9f6a7e155a0fb3.gif", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\b624475d-6b32-4320-85f6-5dea263b44bb_638817196113290573_db1e6f380aca333fca9f6a7e155a0fb3.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4024248"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"6Hy0H9uWqw+3Vagzl7GTEQafajy9ryCdPhOzPvT5/A0=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 15:06:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6Hy0H9uWqw+3Vagzl7GTEQafajy9ryCdPhOzPvT5/A0="}]}, {"Route": "profile-pictures/b624475d-6b32-4320-85f6-5dea263b44bb_638817196113290573_db1e6f380aca333fca9f6a7e155a0fb3.gq6w51lmwl.gif", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\b624475d-6b32-4320-85f6-5dea263b44bb_638817196113290573_db1e6f380aca333fca9f6a7e155a0fb3.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4024248"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"6Hy0H9uWqw+3Vagzl7GTEQafajy9ryCdPhOzPvT5/A0=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 15:06:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gq6w51lmwl"}, {"Name": "label", "Value": "profile-pictures/b624475d-6b32-4320-85f6-5dea263b44bb_638817196113290573_db1e6f380aca333fca9f6a7e155a0fb3.gif"}, {"Name": "integrity", "Value": "sha256-6Hy0H9uWqw+3Vagzl7GTEQafajy9ryCdPhOzPvT5/A0="}]}, {"Route": "profile-pictures/d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817189571640603__Miq1UZK_400x400.dkfjba56rm.jpg", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817189571640603__Miq1UZK_400x400.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "50483"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cwcK2BNY/+Pm9oXMWlb85o76F8CHBZkapTOFGTj5PqI=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 14:55:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dkfjba56rm"}, {"Name": "label", "Value": "profile-pictures/d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817189571640603__Miq1UZK_400x400.jpg"}, {"Name": "integrity", "Value": "sha256-cwcK2BNY/+Pm9oXMWlb85o76F8CHBZkapTOFGTj5PqI="}]}, {"Route": "profile-pictures/d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817189571640603__Miq1UZK_400x400.jpg", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817189571640603__Miq1UZK_400x400.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "50483"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"cwcK2BNY/+Pm9oXMWlb85o76F8CHBZkapTOFGTj5PqI=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 14:55:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cwcK2BNY/+Pm9oXMWlb85o76F8CHBZkapTOFGTj5PqI="}]}, {"Route": "profile-pictures/d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192734685591_c4375c65eb82f284d24f53d6be49e036.dinrx7zu6e.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192734685591_c4375c65eb82f284d24f53d6be49e036.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "969969"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xZzkdTEb+5YCf3TEqLIzSBW/SLXHBnN0fHcepYlH1fE=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 15:01:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dinrx7zu6e"}, {"Name": "label", "Value": "profile-pictures/d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192734685591_c4375c65eb82f284d24f53d6be49e036.png"}, {"Name": "integrity", "Value": "sha256-xZzkdTEb+5YCf3TEqLIzSBW/SLXHBnN0fHcepYlH1fE="}]}, {"Route": "profile-pictures/d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192734685591_c4375c65eb82f284d24f53d6be49e036.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192734685591_c4375c65eb82f284d24f53d6be49e036.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "969969"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xZzkdTEb+5YCf3TEqLIzSBW/SLXHBnN0fHcepYlH1fE=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 15:01:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xZzkdTEb+5YCf3TEqLIzSBW/SLXHBnN0fHcepYlH1fE="}]}, {"Route": "profile-pictures/d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192931813656_ef6c9397f7c66d188591eb75d46c457f.6sfcr98ok6.jpg", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192931813656_ef6c9397f7c66d188591eb75d46c457f.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "111603"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5hnnkHtczIU5NKskw8CE934JMUnsLOTdurJ0NrknKiU=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 15:01:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6sfcr98ok6"}, {"Name": "label", "Value": "profile-pictures/d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192931813656_ef6c9397f7c66d188591eb75d46c457f.jpg"}, {"Name": "integrity", "Value": "sha256-5hnnkHtczIU5NKskw8CE934JMUnsLOTdurJ0NrknKiU="}]}, {"Route": "profile-pictures/d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192931813656_ef6c9397f7c66d188591eb75d46c457f.jpg", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\d88a70a4-ae5d-48aa-85cf-7e43941880f6_638817192931813656_ef6c9397f7c66d188591eb75d46c457f.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "111603"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"5hnnkHtczIU5NKskw8CE934JMUnsLOTdurJ0NrknKiU=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 15:01:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5hnnkHtczIU5NKskw8CE934JMUnsLOTdurJ0NrknKiU="}]}, {"Route": "profile-pictures/defaults/default-picture-profile.d90hbt9f7m.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5153"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 19:17:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d90hbt9f7m"}, {"Name": "label", "Value": "profile-pictures/defaults/default-picture-profile.png"}, {"Name": "integrity", "Value": "sha256-keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q="}]}, {"Route": "profile-pictures/defaults/default-picture-profile.png", "AssetFile": "A:\\DotnetAuth 2 - last\\DotnetAuth\\wwwroot\\profile-pictures\\defaults\\default-picture-profile.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5153"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q=\""}, {"Name": "Last-Modified", "Value": "Fri, 25 Apr 2025 19:17:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-keo2Wop316UIvu5lXMTWLAGv8aEPNkxcGXz+qWBth8Q="}]}]}