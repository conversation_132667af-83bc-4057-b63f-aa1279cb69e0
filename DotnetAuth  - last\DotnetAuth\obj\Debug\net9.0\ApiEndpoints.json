[{"ContainingType": "DotnetAuth.Controllers.TwoFactorController", "Method": "Disable2fa", "RelativePath": "api/2fa/disable", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.Disable2faRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.Disable2faResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.TwoFactorController", "Method": "GenerateRecoveryCodes", "RelativePath": "api/2fa/recovery-codes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.TwoFactorController", "Method": "Setup2fa", "RelativePath": "api/2fa/setup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.Setup2faRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.Setup2faResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.TwoFactorController", "Method": "Get2faStatus", "RelativePath": "api/2fa/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.TwoFactorController", "Method": "VerifyTwoFactorLogin", "RelativePath": "api/2fa/verify", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.TwoFactorLoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.TwoFactorLoginResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.TwoFactorController", "Method": "VerifySetup", "RelativePath": "api/2fa/verify-setup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.Verify2faSetupRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.Verify2faSetupResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "GetAccountActivity", "RelativePath": "api/account-activity", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.AccountActivityResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "ChangeEmail", "RelativePath": "api/change-email", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.ChangeEmailRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.ExternalAuthController", "Method": "GoogleLogin", "RelativePath": "api/external-auth/google", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.GoogleAuthRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.ExternalAuthResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.ExternalAuthController", "Method": "GoogleRegister", "RelativePath": "api/external-auth/google/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.ExternalUserRegistrationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.ExternalAuthResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "ForgotPassword", "RelativePath": "api/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.ForgotPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.UserLoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.UserResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "GetLoginHistory", "RelativePath": "api/login-history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.LoginHistoryResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.ProfilePictureController", "Method": "GetCurrentProfilePicture", "RelativePath": "api/profile-pictures/current", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.ProfilePictureResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.ProfilePictureController", "Method": "GetProfilePictureHistory", "RelativePath": "api/profile-pictures/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.ProfilePictureHistoryResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.ProfilePictureController", "Method": "UploadProfilePicture", "RelativePath": "api/profile-pictures/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Picture", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.ProfilePictureResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "Register", "RelativePath": "api/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.UserRegisterRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "ResetPassword", "RelativePath": "api/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "TwoFactorLogin", "RelativePath": "api/two-factor-login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.TwoFactorLoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.TwoFactorLoginResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "UpdatePhoneNumber", "RelativePath": "api/update-phone", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.UpdatePhoneNumberRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.UpdatePhoneNumberResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "GetById", "RelativePath": "api/user/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "Delete", "RelativePath": "api/user/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "VerifyOtp", "RelativePath": "api/verify-otp", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.VerifyOtpRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DotnetAuth.Controllers.AuthController", "Method": "VerifyPhoneNumber", "RelativePath": "api/verify-phone", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DotnetAuth.Domain.Contracts.VerifyPhoneNumberRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "DotnetAuth.Domain.Contracts.VerifyPhoneNumberResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]