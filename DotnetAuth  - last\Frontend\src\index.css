* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.MuiContainer-root {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.error-message {
  color: #d32f2f;
  margin: 8px 0;
}

/* Add smooth transitions for theme changes */
.MuiPaper-root,
.MuiAppBar-root,
.MuiCard-root,
.MuiButton-root,
.MuiTypography-root {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
