{"openapi": "3.0.1", "info": {"title": "User <PERSON>", "description": "Services to Authenticate user", "version": "v1"}, "paths": {"/api/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserRegisterRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}}}, "/api/verify-otp": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifyOtpRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/two-factor-login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginResponse"}}}}}}}, "/api/forgot-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/change-email": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeEmailRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeEmailRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeEmailRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/update-phone": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePhoneNumberRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePhoneNumberRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePhoneNumberRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UpdatePhoneNumberResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdatePhoneNumberResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePhoneNumberResponse"}}}}}}}, "/api/verify-phone": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyPhoneNumberRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyPhoneNumberRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifyPhoneNumberRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VerifyPhoneNumberResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VerifyPhoneNumberResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyPhoneNumberResponse"}}}}}}}, "/api/user": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/user/{id}": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/login-history": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginHistoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginHistoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginHistoryResponse"}}}}}}}, "/api/account-activity": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AccountActivityResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AccountActivityResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AccountActivityResponse"}}}}}}}, "/api/external-auth/google": {"post": {"tags": ["ExternalAuth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoogleAuthRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoogleAuthRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GoogleAuthRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExternalAuthResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExternalAuthResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExternalAuthResponse"}}}}}}}, "/api/external-auth/google/register": {"post": {"tags": ["ExternalAuth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExternalUserRegistrationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExternalUserRegistrationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExternalUserRegistrationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExternalAuthResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExternalAuthResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExternalAuthResponse"}}}}}}}, "/api/profile-pictures/upload": {"post": {"tags": ["ProfilePicture"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Picture"], "type": "object", "properties": {"Picture": {"type": "string", "format": "binary"}}}, "encoding": {"Picture": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProfilePictureResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProfilePictureResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProfilePictureResponse"}}}}}}}, "/api/profile-pictures/current": {"get": {"tags": ["ProfilePicture"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProfilePictureResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProfilePictureResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProfilePictureResponse"}}}}}}}, "/api/profile-pictures/history": {"get": {"tags": ["ProfilePicture"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProfilePictureHistoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProfilePictureHistoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProfilePictureHistoryResponse"}}}}}}}, "/api/2fa/setup": {"post": {"tags": ["TwoFactor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Setup2faRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Setup2faRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Setup2faRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Setup2faResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Setup2faResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Setup2faResponse"}}}}}}}, "/api/2fa/verify-setup": {"post": {"tags": ["TwoFactor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Verify2faSetupRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Verify2faSetupRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Verify2faSetupRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Verify2faSetupResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Verify2faSetupResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Verify2faSetupResponse"}}}}}}}, "/api/2fa/disable": {"post": {"tags": ["TwoFactor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Disable2faRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Disable2faRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Disable2faRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Disable2faResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Disable2faResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Disable2faResponse"}}}}}}}, "/api/2fa/verify": {"post": {"tags": ["TwoFactor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TwoFactorLoginResponse"}}}}}}}, "/api/2fa/status": {"get": {"tags": ["TwoFactor"], "responses": {"200": {"description": "OK"}}}}, "/api/2fa/recovery-codes": {"post": {"tags": ["TwoFactor"], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AccountActivityEntry": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "activityType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "ipAddress": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AccountActivityResponse": {"type": "object", "properties": {"activities": {"type": "array", "items": {"$ref": "#/components/schemas/AccountActivityEntry"}, "nullable": true}}, "additionalProperties": false}, "ChangeEmailRequest": {"required": ["newEmail", "password"], "type": "object", "properties": {"newEmail": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "Disable2faRequest": {"type": "object", "properties": {"password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Disable2faResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ExternalAuthResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "requiresRegistration": {"type": "boolean"}, "requiresTwoFactor": {"type": "boolean"}}, "additionalProperties": false}, "ExternalUserRegistrationRequest": {"required": ["gender", "idToken", "provider", "role"], "type": "object", "properties": {"provider": {"minLength": 1, "type": "string"}, "idToken": {"minLength": 1, "type": "string"}, "gender": {"$ref": "#/components/schemas/Gender"}, "role": {"$ref": "#/components/schemas/UserRole"}}, "additionalProperties": false}, "ForgotPasswordRequest": {"required": ["confirmPassword", "email", "newPassword", "oldPassword"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "oldPassword": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "Gender": {"enum": [0, 1], "type": "integer", "format": "int32"}, "GoogleAuthRequest": {"required": ["idToken"], "type": "object", "properties": {"idToken": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "LoginHistoryEntry": {"type": "object", "properties": {"loginTime": {"type": "string", "format": "date-time"}, "ipAddress": {"type": "string", "nullable": true}, "device": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "wasSuccessful": {"type": "boolean"}}, "additionalProperties": false}, "LoginHistoryResponse": {"type": "object", "properties": {"loginHistory": {"type": "array", "items": {"$ref": "#/components/schemas/LoginHistoryEntry"}, "nullable": true}}, "additionalProperties": false}, "ProfilePictureHistoryEntry": {"type": "object", "properties": {"fileName": {"type": "string", "nullable": true}, "fileUrl": {"type": "string", "nullable": true}, "uploadDate": {"type": "string", "format": "date-time"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "ProfilePictureHistoryResponse": {"type": "object", "properties": {"history": {"type": "array", "items": {"$ref": "#/components/schemas/ProfilePictureHistoryEntry"}, "nullable": true}}, "additionalProperties": false}, "ProfilePictureResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "fileUrl": {"type": "string", "nullable": true}, "uploadDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "ResetPasswordRequest": {"required": ["confirmPassword", "newPassword"], "type": "object", "properties": {"newPassword": {"minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "Setup2faRequest": {"type": "object", "properties": {"twoFactorType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Setup2faResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "secret": {"type": "string", "nullable": true}, "qrCodeUrl": {"type": "string", "nullable": true}, "verificationCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TwoFactorLoginRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "twoFactorCode": {"type": "string", "nullable": true}, "rememberDevice": {"type": "boolean"}}, "additionalProperties": false}, "TwoFactorLoginResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdatePhoneNumberRequest": {"required": ["phoneNumber"], "type": "object", "properties": {"phoneNumber": {"minLength": 1, "type": "string", "format": "tel"}}, "additionalProperties": false}, "UpdatePhoneNumberResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "verificationCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserLoginRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "rememberMe": {"type": "boolean"}}, "additionalProperties": false}, "UserRegisterRequest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "gender": {"$ref": "#/components/schemas/Gender"}, "role": {"$ref": "#/components/schemas/UserRole"}}, "additionalProperties": false}, "UserResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "gender": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "isEmailConfirmed": {"type": "boolean"}, "createAt": {"type": "string", "format": "date-time"}, "updateAt": {"type": "string", "format": "date-time"}, "accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserRole": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Verify2faSetupRequest": {"type": "object", "properties": {"verificationCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Verify2faSetupResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "recoveryCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "VerifyOtpRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "otp": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VerifyPhoneNumberRequest": {"required": ["phoneNumber", "verificationCode"], "type": "object", "properties": {"phoneNumber": {"minLength": 1, "type": "string", "format": "tel"}, "verificationCode": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "VerifyPhoneNumberResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "Please enter a valid token in the following format: {your token here} do not add the word 'Bearer' before it.", "scheme": "Bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}